use aes_gcm::{Aes256Gcm, Key, Nonce, KeyInit};
use aes_gcm::aead::{Aead, OsRng};
use pbkdf2::{pbkdf2_hmac};
use sha2::Sha256;
use base64::{Engine as _, engine::general_purpose};
use rand::RngCore;
use std::error::Error;
use std::fmt;
use tracing::{debug, info, warn};

/// Encryption configuration and utilities for RustyCluster
/// 
/// This module provides AES-256-GCM encryption for sensitive configuration data
/// such as passwords and authentication credentials.
pub struct CryptoManager {
    master_key: [u8; 32], // 256-bit key for AES-256
}

/// Custom error type for cryptographic operations
#[derive(Debug)]
pub enum CryptoError {
    EncryptionFailed(String),
    DecryptionFailed(String),
    InvalidFormat(String),
    KeyDerivationFailed(String),
}

impl fmt::Display for CryptoError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            CryptoError::EncryptionFailed(msg) => write!(f, "Encryption failed: {}", msg),
            CryptoError::DecryptionFailed(msg) => write!(f, "Decryption failed: {}", msg),
            CryptoError::InvalidFormat(msg) => write!(f, "Invalid format: {}", msg),
            CryptoError::KeyDerivationFailed(msg) => write!(f, "Key derivation failed: {}", msg),
        }
    }
}

impl Error for CryptoError {}

impl CryptoManager {
    /// Create a new CryptoManager with a master password
    /// 
    /// The master password is used to derive the encryption key using PBKDF2
    /// with a fixed salt for consistency across application restarts.
    pub fn new(master_password: &str) -> Result<Self, CryptoError> {
        info!("Initializing crypto manager");
        
        // Use a fixed salt for consistency - in production, this could be configurable
        // or stored securely. For RustyCluster, we use a fixed salt to ensure
        // the same master password always generates the same key.
        let salt = b"rustycluster_salt_v1"; // 20 bytes
        let mut key = [0u8; 32];
        
        // Derive key using PBKDF2 with 100,000 iterations (recommended minimum)
        pbkdf2_hmac::<Sha256>(master_password.as_bytes(), salt, 100_000, &mut key);
        
        debug!("Crypto manager initialized successfully");
        Ok(Self { master_key: key })
    }

    /// Encrypt a plaintext string and return base64-encoded encrypted data
    /// 
    /// Format: ENC:base64(salt:nonce:encrypted_data)
    /// The ENC: prefix indicates this is an encrypted value.
    pub fn encrypt(&self, plaintext: &str) -> Result<String, CryptoError> {
        if plaintext.is_empty() {
            return Ok(String::new());
        }

        // Generate a random 12-byte nonce for AES-GCM
        let mut nonce_bytes = [0u8; 12];
        OsRng.fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);

        // Create cipher instance
        let key = Key::<Aes256Gcm>::from_slice(&self.master_key);
        let cipher = Aes256Gcm::new(key);

        // Encrypt the plaintext
        let ciphertext = cipher.encrypt(nonce, plaintext.as_bytes())
            .map_err(|e| CryptoError::EncryptionFailed(format!("AES-GCM encryption failed: {}", e)))?;

        // Combine nonce and ciphertext for storage
        let mut combined = Vec::with_capacity(12 + ciphertext.len());
        combined.extend_from_slice(&nonce_bytes);
        combined.extend_from_slice(&ciphertext);

        // Encode as base64 and add ENC: prefix
        let encoded = general_purpose::STANDARD.encode(&combined);
        Ok(format!("ENC:{}", encoded))
    }

    /// Decrypt a base64-encoded encrypted string
    /// 
    /// Handles both encrypted (ENC: prefix) and plain text values for backward compatibility
    pub fn decrypt(&self, encrypted_data: &str) -> Result<String, CryptoError> {
        if encrypted_data.is_empty() {
            return Ok(String::new());
        }

        // Check if the data is encrypted (has ENC: prefix)
        if !encrypted_data.starts_with("ENC:") {
            // Plain text - return as-is for backward compatibility
            debug!("Decrypting plain text value (backward compatibility)");
            return Ok(encrypted_data.to_string());
        }

        // Remove ENC: prefix and decode base64
        let encoded_data = &encrypted_data[4..]; // Skip "ENC:"
        let combined = general_purpose::STANDARD.decode(encoded_data)
            .map_err(|e| CryptoError::InvalidFormat(format!("Base64 decode failed: {}", e)))?;

        // Ensure we have at least nonce (12 bytes) + some ciphertext
        if combined.len() < 13 {
            return Err(CryptoError::InvalidFormat("Encrypted data too short".to_string()));
        }

        // Split nonce and ciphertext
        let (nonce_bytes, ciphertext) = combined.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);

        // Create cipher instance and decrypt
        let key = Key::<Aes256Gcm>::from_slice(&self.master_key);
        let cipher = Aes256Gcm::new(key);

        let plaintext = cipher.decrypt(nonce, ciphertext)
            .map_err(|e| CryptoError::DecryptionFailed(format!("AES-GCM decryption failed: {}", e)))?;

        String::from_utf8(plaintext)
            .map_err(|e| CryptoError::DecryptionFailed(format!("UTF-8 conversion failed: {}", e)))
    }

    /// Check if a string is encrypted (has ENC: prefix)
    pub fn is_encrypted(data: &str) -> bool {
        data.starts_with("ENC:")
    }

    /// Decrypt Redis URL by extracting and decrypting password components
    /// 
    /// Handles various Redis URL formats:
    /// - redis://username:password@host:port
    /// - redis://:password@host:port  
    /// - redis://username@host:port
    pub fn decrypt_redis_url(&self, redis_url: &str) -> Result<String, CryptoError> {
        if redis_url.is_empty() || !redis_url.contains('@') {
            // No authentication in URL, return as-is
            return Ok(redis_url.to_string());
        }

        // Parse the URL to extract components
        let parts: Vec<&str> = redis_url.split('@').collect();
        if parts.len() != 2 {
            return Ok(redis_url.to_string()); // Invalid format, return as-is
        }

        let auth_part = parts[0];
        let host_part = parts[1];

        // Extract the protocol and auth info
        if let Some(protocol_end) = auth_part.find("://") {
            let protocol = &auth_part[..protocol_end + 3]; // Include "://"
            let auth_info = &auth_part[protocol_end + 3..];

            // Handle different authentication formats
            let decrypted_auth = if auth_info.contains(':') {
                // Format: username:password or :password
                let auth_parts: Vec<&str> = auth_info.splitn(2, ':').collect();
                if auth_parts.len() == 2 {
                    let username = auth_parts[0];
                    let password = auth_parts[1];
                    
                    // Decrypt password if encrypted
                    let decrypted_password = self.decrypt(password)?;
                    
                    if username.is_empty() {
                        format!(":{}", decrypted_password) // :password format
                    } else {
                        format!("{}:{}", username, decrypted_password) // username:password format
                    }
                } else {
                    auth_info.to_string()
                }
            } else {
                // Format: username only (treated as password by Redis)
                let decrypted_username = self.decrypt(auth_info)?;
                decrypted_username
            };

            Ok(format!("{}{}@{}", protocol, decrypted_auth, host_part))
        } else {
            // Invalid URL format
            Ok(redis_url.to_string())
        }
    }
}

/// Utility function to create a crypto manager from environment or default
/// 
/// Looks for RUSTYCLUSTER_MASTER_KEY environment variable, otherwise uses a default key.
/// In production, the master key should be provided via environment variable or secure configuration.
pub fn create_crypto_manager() -> Result<CryptoManager, CryptoError> {
    let master_key = std::env::var("RUSTYCLUSTER_MASTER_KEY")
        .unwrap_or_else(|_| {
            warn!("RUSTYCLUSTER_MASTER_KEY not set, using default key. Set this environment variable in production!");
            "rustycluster_default_master_key_change_in_production".to_string()
        });

    CryptoManager::new(&master_key)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encrypt_decrypt() {
        let crypto = CryptoManager::new("test_master_password").unwrap();
        
        let plaintext = "my_secret_password";
        let encrypted = crypto.encrypt(plaintext).unwrap();
        
        assert!(encrypted.starts_with("ENC:"));
        assert_ne!(encrypted, plaintext);
        
        let decrypted = crypto.decrypt(&encrypted).unwrap();
        assert_eq!(decrypted, plaintext);
    }

    #[test]
    fn test_backward_compatibility() {
        let crypto = CryptoManager::new("test_master_password").unwrap();
        
        let plaintext = "plain_password";
        let decrypted = crypto.decrypt(plaintext).unwrap();
        
        assert_eq!(decrypted, plaintext);
    }

    #[test]
    fn test_redis_url_decryption() {
        let crypto = CryptoManager::new("test_master_password").unwrap();
        
        // Test with encrypted password
        let password = "secret123";
        let encrypted_password = crypto.encrypt(password).unwrap();
        let redis_url = format!("redis://user:{}@localhost:6379", encrypted_password);
        
        let decrypted_url = crypto.decrypt_redis_url(&redis_url).unwrap();
        assert_eq!(decrypted_url, "redis://user:secret123@localhost:6379");
    }

    #[test]
    fn test_empty_values() {
        let crypto = CryptoManager::new("test_master_password").unwrap();
        
        assert_eq!(crypto.encrypt("").unwrap(), "");
        assert_eq!(crypto.decrypt("").unwrap(), "");
    }
}
