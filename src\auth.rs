use std::sync::Arc;
use std::time::{Duration, Instant};
use tonic::Status;
use uuid::Uuid;
use tracing::{error, info};
use dashmap::DashMap;

// Session information - optimized for performance
#[derive(Debug, <PERSON>lone)]
struct Session {
    expires_at: Instant,
}

// High-performance authentication service
#[derive(Debug, Clone)]
pub struct AuthService {
    // Configuration
    auth_enabled: bool,

    auth_username: String,
    auth_password: String,
    auth_mode: String, // "connection_only" or "per_request"
    auth_token_expiry_enabled: bool,

    // Active sessions - using DashMap for lock-free concurrent access
    // This eliminates RwLock contention which was a major bottleneck
    sessions: Arc<DashMap<String, Session>>,

    // Session configuration
    session_duration: Duration,
}

impl AuthService {
    pub fn new(auth_enabled: bool, _auth_token: String, auth_username: String, auth_password: String, auth_mode: String, auth_token_expiry_enabled: bool) -> Self {
        Self {
            auth_enabled,

            auth_username,
            auth_password,
            auth_mode,
            auth_token_expiry_enabled,
            // Use DashMap for lock-free concurrent session storage
            sessions: Arc::new(DashMap::new()),
            session_duration: Duration::from_secs(3600), // 1 hour by default
        }
    }

    // Set session duration
    pub fn with_session_duration(mut self, duration: Duration) -> Self {
        self.session_duration = duration;
        self
    }

    // Check if authentication is enabled
    // Inlined for maximum performance in hot path
    #[inline(always)]
    pub fn is_auth_enabled(&self) -> bool {
        self.auth_enabled
    }





    // Authenticate with username and password
    pub async fn authenticate_credentials(&self, username: &str, password: &str) -> Result<String, Status> {
        if !self.auth_enabled {
            // Authentication is disabled, allow all requests
            return Ok(String::new());
        }

        // Check if the credentials match the configured credentials
        if !self.auth_username.is_empty() && !self.auth_password.is_empty() &&
           username == self.auth_username && password == self.auth_password {
            // Generate a session token
            let session_token = Uuid::new_v4().to_string();
            let now = Instant::now();

            // Create a new session with conditional expiry
            let expires_at = if self.auth_token_expiry_enabled {
                now + self.session_duration
            } else {
                // Set expiry far in the future for permanent tokens
                now + Duration::from_secs(u64::MAX / 2) // Effectively permanent
            };

            let session = Session {
                expires_at,
            };

            // Store the session using lock-free DashMap
            self.sessions.insert(session_token.clone(), session);

            info!("Credential authentication successful, session created");
            return Ok(session_token);
        }

        // Credential authentication failed
        error!("Credential authentication failed for user: {}", username);
        Err(Status::unauthenticated("Invalid username or password"))
    }

    // High-performance session validation - optimized for 10K+ RPS
    #[inline(always)]
    pub async fn validate_session(&self, token: &str) -> Result<(), Status> {
        if !self.auth_enabled {
            // Authentication is disabled, allow all requests
            return Ok(());
        }

        // Fast path: Use DashMap for lock-free concurrent access
        // This eliminates the RwLock contention that was causing performance issues
        if let Some(session_ref) = self.sessions.get(token) {
            // For connection-only mode, skip expiry check if expiry is disabled
            if self.auth_mode == "connection_only" && !self.auth_token_expiry_enabled {
                // Session is valid and permanent - no expiry check needed
                return Ok(());
            }

            let now = Instant::now();

            // Check expiration without additional allocations
            if session_ref.expires_at > now {
                // Session is valid - no logging in hot path for performance
                return Ok(());
            } else {
                // Session expired - remove it asynchronously to avoid blocking
                let token_owned = token.to_string();
                let sessions_clone = self.sessions.clone();

                // Remove expired session in background to avoid blocking the request
                tokio::spawn(async move {
                    sessions_clone.remove(&token_owned);
                });

                return Err(Status::unauthenticated("Session expired"));
            }
        }

        // Session not found - minimal logging for performance
        Err(Status::unauthenticated("Invalid session token"))
    }

    // High-performance cleanup of expired sessions
    pub async fn cleanup_expired_sessions(&self) {
        let now = Instant::now();

        // Use DashMap's efficient iteration and removal
        // This doesn't block other operations like RwLock would
        self.sessions.retain(|_token, session| {
            session.expires_at > now
        });
    }

    // Start a background task to periodically clean up expired sessions
    pub fn start_cleanup_task(self: Arc<Self>) {
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(300)); // Clean up every 5 minutes

            loop {
                interval.tick().await;
                self.cleanup_expired_sessions().await;
            }
        });
    }
}


